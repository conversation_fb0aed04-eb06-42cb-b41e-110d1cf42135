package com.yxt.talent.rv.application.democopy;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.spsdk.common.component.ExpressionCalc;
import com.yxt.spsdk.common.component.SpRuleService;
import com.yxt.spsdk.democopy.DemoCopyRunner;
import com.yxt.spsdk.democopy.PreSetIdMapRepository;
import com.yxt.talent.rv.application.dmp.calc.DmpCalculator;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.common.enums.FormulaTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import com.yxt.talent.rv.infrastructure.persistence.cache.RedisRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.repository.xpd.DimGridLevelRuleDTO;
import com.yxt.udpfacade.bean.demo.OrgInit4Mq;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.AOM_REF_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.DYN_SPM_LABEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.DYN_XPD_ACTION_PLAN_TARGET_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.O2O_TRAINING_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPBK_POOL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPEVAL_DIM_SETTING_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_INDICATOR_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPM_LABEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_RESULT_CONF_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_ACTV_PROF_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_PERF_GRADE_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_PERF_PERIOD_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_XPD_GRID_CELL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_XPD_GRID_LEVEL_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_XPD_ID;
import static com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants.SPRV_XPD_RULE_CONF_ID;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.tryCall;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.withContext;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@Service
@RequiredArgsConstructor
public class DemoTableProvider {

    private final RedisRepo redisRepo;
    private final PerfGradeMapper perfGradeMapper;
    private final PerfPeriodMapper perfPeriodMapper;
    private final PerfMapper perfMapper;
    private final XpdMapper xpdMapper;
    private final XpdActionPlanMapper xpdActionPlanMapper;
    private final XpdDimMapper xpdDimMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdDimRuleMapper xpdDimRuleMapper;
    private final XpdDimRuleCalcMapper xpdDimRuleCalcMapper;
    private final XpdGridMapper xpdGridMapper;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final XpdGridDimCombMapper xpdGridDimCombMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdGridRatioMapper xpdGridRatioMapper;
    private final XpdImportMapper xpdImportMapper;
    private final XpdImportDimUserMapper xpdImportDimUserMapper;
    private final XpdImportIndicatorUserMapper xpdImportIndicatorUserMapper;
    private final XpdImportLogMapper xpdImportLogMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final XpdResultIndicatorMapper xpdResultIndicatorMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdUserExtMapper xpdUserExtMapper;
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final XpdRuleCalcIndicatorMapper xpdRuleCalcIndicatorMapper;
    private final XpdRuleCalcDimMapper xpdRuleCalcDimMapper;
    private final XpdRuleMapper xpdRuleMapper;
    private final XpdResultUserIndicatorMapper xpdResultUserIndicatorMapper;
    private final XpdResultUserDimcombMapper xpdResultUserDimcombMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final ActivityPerfMapper activityPerfMapper;
    private final ActivityPerfConfMapper activityPerfConfMapper;
    private final ActivityPerfResultMapper activityPerfResultMapper;
    private final ActivityPerfResultConfMapper activityPerfResultConfMapper;
    private final ActivityProfileMapper activityProfileMapper;
    private final ActivityProfileResultDetailMapper activityProfileResultDetailMapper;
    private final ActivityProfileResultMapper activityProfileResultMapper;
    private final ActivityProfileIndicatorMapper activityProfileIndicatorMapper;
    private final SpRuleService spRuleService;

    public DemoCopyRunner buildRunner(OrgInit4Mq orgInit) {

        DemoCopyRunner runner = new DemoCopyRunner();
        runner.initCopyCtx(orgInit);

        // 设置被其他服务在复制时依赖的id
        setDependentIdKeys(runner);

        // 盘点基础信息表
        addBase(runner);
        // 绩效管理
        addPerf(runner);
        // 新盘点
        addXpd(runner);
        // aom活动
        addActv(runner);
        // TODO 校准会
        addCali(runner);

        return runner;
    }

    private void addActv(DemoCopyRunner runner) {
        // 处理一对多字段：即我们一个字段存储了外部系统多个表主键，根据type区分的情况
        runner.addDynamicIdMap(
            DYN_SPM_LABEL_ID, (target, copyCtx) -> {
                ActivityProfileResultDetailPO entity = (ActivityProfileResultDetailPO) target;
                if (DmpCalculator.DimLabelTypeEnum.isLabel(entity.getLabelType())) {
                    // 标签
                    return SPM_LABEL_ID;
                } else {
                    // 指标
                    return SPM_INDICATOR_ID;
                }
            });

        // rv_activity_perf
        runner.addCopyEntity(
            ActivityPerfPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityPerfMapper.selectByOrgId(sourceOrgId);
            },entities -> {
                for (ActivityPerfPO entity : entities) {
                    // 替换 periodIds
                    entity.setPeriodIds(replacePeriodIds(runner, entity.getPeriodIds()));
                }
                entities.forEach(activityPerfMapper::insertOrUpdate);
            });

        // rv_activity_perf_conf
        runner.addCopyEntity(
            ActivityPerfConfPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityPerfConfMapper.selectByOrgId(sourceOrgId);
            },entities -> {
                entities.forEach(activityPerfConfMapper::insertOrUpdate);
            });

        // rv_activity_perf_result
        runner.addCopyEntity(
            ActivityPerfResultPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityPerfResultMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(activityPerfResultMapper::insertOrUpdate);
            });

        // rv_activity_perf_result_conf
        runner.addCopyEntity(
            ActivityPerfResultConfPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityPerfResultConfMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                for (ActivityPerfResultConfPO entity : entities) {
                    // 替换ruleConf
                    entity.setRuleConf(replaceRule(runner, entity.getRuleConf()));
                }
                entities.forEach(activityPerfResultConfMapper::insertOrUpdate);
            });

        // rv_activity_profile
        runner.addCopyEntity(
            ActivityProfilePO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityProfileMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(activityProfileMapper::insertOrUpdate);
            });

        // rv_activity_profile_indicator
        runner.addCopyEntity(
            ActivityProfileIndicatorPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityProfileIndicatorMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(activityProfileIndicatorMapper::insertOrUpdate);
            });

        // rv_activity_profile_result
        runner.addCopyEntity(
            ActivityProfileResultPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityProfileResultMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(activityProfileResultMapper::insertOrUpdate);
            });

        // rv_activity_profile_result_detail
        runner.addCopyEntity(
            ActivityProfileResultDetailPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return activityProfileResultDetailMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(activityProfileResultDetailMapper::insertOrUpdate);
            });

    }

    private String replacePeriodIds(DemoCopyRunner runner, String periodIds) {
        if (StringUtils.isNotBlank(periodIds)) {
            return Arrays.stream(periodIds.split(";"))
                .map(periodId -> runner.getIdMapValue(SPRV_PERF_PERIOD_ID, periodId))
                .collect(Collectors.joining(";"));
        }
        return EMPTY;
    }

    private void addXpd(DemoCopyRunner runner) {
        runner.addDynamicIdMap(
            DYN_XPD_ACTION_PLAN_TARGET_ID, (target, copyCtx) -> {
                XpdActionPlanPO entity = (XpdActionPlanPO) target;
                // 行动计划类型：0-培训项目, 1-人才池
                if (entity.getTargetType() == 0) {
                    // 培训项目
                    return O2O_TRAINING_ID;
                } else {
                    // 人才池
                    return SPBK_POOL_ID;
                }
            });

        // xpd
        runner.addCopyEntity(
            XpdPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdMapper::insertOrUpdate);
            });

        // xpd_action_plan
        runner.addCopyEntity(
            XpdActionPlanPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdActionPlanMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdActionPlanMapper::insertOrUpdate);
            });

        // xpd_dim
        runner.addCopyEntity(
            XpdDimPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdDimMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdDimMapper::insertOrUpdate);
            });

        // xpd_dim_comb
        runner.addCopyEntity(
            XpdDimCombPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdDimCombMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdDimCombMapper::insertOrUpdate);
            });

        // rv_xpd_dim_rule
        runner.addCopyEntity(
            XpdDimRulePO.class,
            runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdDimRuleMapper.selectByOrgId(sourceOrgId);
            },
            entities -> {
                for (XpdDimRulePO rule : entities) {
                    // 替换levelRule
                    rule.setLevelRule(replaceGridLevelRule(runner, rule.getLevelRule()));

                    // 替换高级公式
                    Pair<String, String> formulaPair =
                        demoCopyFormula(runner, rule.getFormula(), rule.getFormulaExpCode());
                    rule.setFormula(formulaPair.getKey());
                    rule.setFormulaExpCode(formulaPair.getValue());

                    // 替换aomActId
                    rule.setAomActId(runner.getIdMapValue(AOM_REF_ID, rule.getAomActId()));
                }
                entities.forEach(xpdDimRuleMapper::insertOrUpdate);
            }
        );

        // rv_xpd_dim_rule_calc
        runner.addCopyEntity(
            XpdDimRuleCalcPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdDimRuleCalcMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                for (XpdDimRuleCalcPO calc : entities) {
                    // 替换refIds
                    calc.setRefIds(replaceRefIds(runner, calc.getRefIds()));
                }
                entities.forEach(xpdDimRuleCalcMapper::insertOrUpdate);
            });

        // rv_xpd_grid
        runner.addCopyEntity(
            XpdGridPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdGridMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdGridMapper::insertOrUpdate);
            });

        // rv_xpd_grid_cell
        runner.addCopyEntity(
            XpdGridCellPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdGridCellMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdGridCellMapper::insertOrUpdate);
            });

        // rv_xpd_grid_dim_comb
        runner.addCopyEntity(
            XpdGridDimCombPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdGridDimCombMapper.selectByOrgId(sourceOrgId);
            },entities -> {
                entities.forEach(xpdGridDimCombMapper::insertOrUpdate);
            });

        // rv_xpd_grid_level
        runner.addCopyEntity(
            XpdGridLevelPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdGridLevelMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdGridLevelMapper::insertOrUpdate);
            });

        // rv_xpd_grid_ratio
        runner.addCopyEntity(
            XpdGridRatioPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdGridRatioMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                // 替换 gridCellIds
                for (XpdGridRatioPO ratio : entities) {
                    ratio.setGridCellIds(replaceGridCellIds(runner, ratio.getGridCellIds()));
                }
                entities.forEach(xpdGridRatioMapper::insertOrUpdate);
            });

        // rv_xpd_import
        runner.addCopyEntity(
            XpdImportPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdImportMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdImportMapper::insertOrUpdate);
            });

        // rv_xpd_import_dim_user
        runner.addCopyEntity(
            XpdImportDimUserPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdImportDimUserMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdImportDimUserMapper::insertOrUpdate);
            });

        // rv_xpd_import_indicator_user
        runner.addCopyEntity(
            XpdImportIndicatorUserPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdImportIndicatorUserMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdImportIndicatorUserMapper::insertOrUpdate);
            });

        // rv_xpd_import_log
        runner.addCopyEntity(
            XpdImportLogPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdImportLogMapper.selectByOrgId(sourceOrgId);
            },entities -> {
                entities.forEach(xpdImportLogMapper::insertOrUpdate);
            });

        // rv_xpd_level
        runner.addCopyEntity(
            XpdLevelPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdLevelMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                // 替换 formula
                for (XpdLevelPO level : entities) {
                    level.setFormula(replaceRule(runner, level.getFormula()));
                }
                entities.forEach(xpdLevelMapper::insertOrUpdate);
            });

        // rv_xpd_result_indicator
        runner.addCopyEntity(
            XpdResultIndicatorPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdResultIndicatorMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdResultIndicatorMapper::insertOrUpdate);
            });

        // rv_xpd_result_user
        runner.addCopyEntity(
            XpdResultUserPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdResultUserMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                for (XpdResultUserPO user : entities) {
                    // 替换 originalSnap
                    user.setOriginalSnap(null);
                }
                entities.forEach(xpdResultUserMapper::insertOrUpdate);
            });

        // rv_xpd_result_user_dim
        runner.addCopyEntity(
            XpdResultUserDimPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdResultUserDimMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                // 置空 originalSnap
                for (XpdResultUserDimPO dim : entities) {
                    dim.setOriginalSnap(null);
                }
                entities.forEach(xpdResultUserDimMapper::insertOrUpdate);
            });

        // rv_xpd_result_user_dimcomb
        runner.addCopyEntity(
            XpdResultUserDimcombPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdResultUserDimcombMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                // 置空 originalSnap
                for (XpdResultUserDimcombPO dim : entities) {
                    dim.setOriginalSnap(null);
                }
                entities.forEach(xpdResultUserDimcombMapper::insertOrUpdate);
            });

        // rv_xpd_result_user_indicator
        runner.addCopyEntity(
            XpdResultUserIndicatorPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdResultUserIndicatorMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                for (XpdResultUserIndicatorPO indicator : entities) {
                    // 置空 originalSnap
                    indicator.setOriginalSnap(null);

                    // 处理 resultDetail
                    indicator.setResultDetail(replaceResultDetail(runner, indicator.getResultDetail()));
                }
                entities.forEach(xpdResultUserIndicatorMapper::insertOrUpdate);
            });

        // rv_xpd_rule
        runner.addCopyEntity(
            XpdRulePO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdRuleMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                for (XpdRulePO rule : entities) {
                    // 替换 formula 和 formulaExpCode
                    Pair<String, String> formulaPair =
                        demoCopyFormula(runner, rule.getFormula(), rule.getFormulaExpCode());
                    rule.setFormula(formulaPair.getKey());
                    rule.setFormulaExpCode(formulaPair.getValue());
                }
                entities.forEach(xpdRuleMapper::insertOrUpdate);
            });

        // rv_xpd_rule_calc_dim
        runner.addCopyEntity(
            XpdRuleCalcDimPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdRuleCalcDimMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdRuleCalcDimMapper::insertOrUpdate);
            });

        // rv_xpd_rule_calc_indicator
        runner.addCopyEntity(
            XpdRuleCalcIndicatorPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdRuleCalcIndicatorMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                for (XpdRuleCalcIndicatorPO indicator : entities) {
                    // 替换 refIds
                    indicator.setRefIds(replaceRefIds(runner, indicator.getRefIds()));
                }
                entities.forEach(xpdRuleCalcIndicatorMapper::insertOrUpdate);
            });

        // rv_xpd_rule_conf
        runner.addCopyEntity(
            XpdRuleConfPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdRuleConfMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                for (XpdRuleConfPO conf : entities) {
                    // 替换 snap
                    conf.setSnap(replaceSnap(runner, conf.getSnap()));
                }
                entities.forEach(xpdRuleConfMapper::insertOrUpdate);
            });

        // rv_xpd_user_ext
        runner.addCopyEntity(
            XpdUserExtPO.class, runCtx -> {
                String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
                return xpdUserExtMapper.selectByOrgId(sourceOrgId);
            }, entities -> {
                entities.forEach(xpdUserExtMapper::insertOrUpdate);
            });
    }

    @Nullable
    private String replaceSnap(DemoCopyRunner runner, String snap) {
        if (StringUtils.isBlank(snap)) {
            return EMPTY;
        }
        XpdRuleConfFastDto ruleConfFastDto = tryCall(withContext(() -> JSON.parseObject(snap, XpdRuleConfFastDto.class), snap));
        if (ruleConfFastDto == null) {
            return EMPTY;
        }
        ruleConfFastDto.setXpdId(runner.getIdMapValue(SPRV_XPD_ID, ruleConfFastDto.getXpdId()));
        ruleConfFastDto.setRuleConfId(runner.getIdMapValue(SPRV_XPD_RULE_CONF_ID, ruleConfFastDto.getRuleConfId()));
        ruleConfFastDto.setRuleConfVersion(1);

        ruleConfFastDto.getLevelRuleList().forEach(level -> {
            level.setGridLevelId(runner.getIdMapValue(SPRV_XPD_GRID_LEVEL_ID, level.getGridLevelId()));
            // TODO 在这个表中，这些字段好像都没有设置值？？？
            level.setMatchValues(new ArrayList<>());
            level.setPerfresults(new ArrayList<>());
            level.setMatchValueList(new ArrayList<>());
            level.setJudgeRule(replaceRule(runner, level.getJudgeRule()));
        });

        return tryCall(withContext(() -> JSON.toJSONString(ruleConfFastDto), ruleConfFastDto));
    }

    private String replaceResultDetail(DemoCopyRunner runner, String resultDetail) {
        List<DimUserIndicatorResultDto> resultList =
            tryCall(withContext(() -> JSON.parseArray(resultDetail, DimUserIndicatorResultDto.class), resultDetail));
        if (CollectionUtils.isEmpty(resultList)) {
            return EMPTY;
        }
        for (DimUserIndicatorResultDto resultDto : resultList) {
            List<String> perfResultIds = resultDto.getLevelValues();
            if (CollectionUtils.isNotEmpty(perfResultIds)) {
                perfResultIds.replaceAll(sourceId -> runner.getIdMapValue(SPRV_ACTV_PERF_RESULT_CONF_ID, sourceId));
            }
        }
        return tryCall(withContext(() -> JSON.toJSONString(resultList), resultList));
    }

    /**
     * 替换公式中的ID
     * @param runner  DemoCopyRunner实例
     * @param formula 需要处理的公式字符串
     * @return 处理后的公式字符串
     */
    private String replaceRule(DemoCopyRunner runner, String formula) {
        if (StringUtils.isNotBlank(formula)) {
            SpRuleBean ruleBean = tryCall(withContext(() -> JSON.parseObject(formula, SpRuleBean.class), formula));
            spRuleService.demoCopyReplaceId(runner, ruleBean);
            return tryCall(withContext(() -> JSON.toJSONString(ruleBean), ruleBean));
        }
        return formula;
    }

    /**
     * 替换公式中的ID
     * @param runner DemoCopyRunner实例
     * @return 处理后的公式字符串
     */
    private SpRuleBean replaceRule(DemoCopyRunner runner, SpRuleBean ruleBean) {
        if (ruleBean != null) {
            spRuleService.demoCopyReplaceId(runner, ruleBean);
        }
        return ruleBean;
    }

    /**
     * 替换格子ID
     * @param runner DemoCopyRunner实例
     * @param gridCellIds 需要处理的格子ID字符串，以分号分隔
     * @return 处理后的格子ID字符串
     */
    private String replaceGridCellIds(DemoCopyRunner runner, String gridCellIds) {
        String cellIdsStr = Optional.ofNullable(gridCellIds).orElse("");
        if (StringUtils.isBlank(cellIdsStr)) {
            return cellIdsStr;
        }

        return Arrays.stream(cellIdsStr.split(";"))
            .map(cellId -> runner.getIdMapValue(SPRV_XPD_GRID_CELL_ID, cellId))
            .collect(Collectors.joining(";"));
    }

    /**
     * 替换引用ID
     * @param runner DemoCopyRunner实例
     * @param refIds 需要处理的引用ID JSON字符串
     * @return 处理后的引用ID JSON字符串
     */
    private static String replaceRefIds(DemoCopyRunner runner, String refIds) {
        if (StringUtils.isNotBlank(refIds)) {
            List<XpdDimRuleCalcRefDto> refList = tryCall(withContext(() -> JSON.parseArray(refIds, XpdDimRuleCalcRefDto.class), refIds));
            if (CollectionUtils.isNotEmpty(refList)) {
                refList.forEach(
                    ref -> {
                        ref.setRefId(runner.getIdMapValue(AOM_REF_ID, ref.getRefId()));
                    }
                );
                return tryCall(withContext(() -> JSON.toJSONString(refList), refList));
            }
        }
        return refIds;
    }

    /**
     * 替换levelRule
     *
     * @param runner    DemoCopyRunner实例
     * @param levelRule 需要处理的levelRule JSON字符串
     * @return 处理后的levelRule JSON字符串
     */
    private String replaceGridLevelRule(DemoCopyRunner runner, String levelRule) {
        if (!StringUtils.isNotBlank(levelRule)) {
            return levelRule;
        }
        List<DimGridLevelRuleDTO> levelRuleList = tryCall(withContext(() -> JSON.parseArray(levelRule, DimGridLevelRuleDTO.class), levelRule));
        if (!CollectionUtils.isNotEmpty(levelRuleList)) {
            return levelRule;
        }
        for (DimGridLevelRuleDTO level : levelRuleList) {
            level.setGridLevelId(
                runner.getIdMapValue(SPRV_XPD_GRID_LEVEL_ID, level.getGridLevelId())
            );
        }
        return tryCall(withContext(() -> JSON.toJSONString(levelRuleList), levelRuleList));
    }

    private void setDependentIdKeys(DemoCopyRunner runner) {
        String[] dependentIdKeys = {
            SPRV_PERF_PERIOD_ID,
            SPRV_PERF_GRADE_ID,
            SPRV_XPD_ID,
            SPRV_XPD_RULE_CONF_ID,
            SPRV_XPD_GRID_LEVEL_ID,
            SPRV_XPD_GRID_CELL_ID,
            SPRV_ACTV_PERF_ID,
            SPRV_ACTV_PERF_RESULT_CONF_ID,
            SPRV_ACTV_PROF_ID
        };
        runner.setPreSetIdMapKey(new PreSetIdMapRepository() {
            @Override
            public void saveValue(String idMapKey, String subKey, String value) {
                String saveKey = String.format(RedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                redisRepo.setHmValue(
                        saveKey, subKey, value, AppConstants.DEMO_COPY_RUN_DATA_KEEP_TIME);
            }

            @Override
            public String queryValue(String idMapKey, String subKey) {
                String saveKey = String.format(RedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                return redisRepo.getHmValue(saveKey, subKey);
            }

            @Override
            public void delByIdMapKey(String idMapKey) {
                String saveKey = String.format(RedisKeys.CACHE_COPY_ORG_IDMAP, idMapKey);
                redisRepo.removeKey(saveKey);
            }
        }, dependentIdKeys);
    }

    private void addBase(DemoCopyRunner runner) {
        // 暂无
    }

    private void addCali(DemoCopyRunner runner) {
    }

    private void addPerf(DemoCopyRunner runner) {
        runner.addCopyEntity(PerfGradePO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return perfGradeMapper.selectByOrgIdIncludeDeleted(sourceOrgId);
        }, list ->{
            perfGradeMapper.deleteByOrgId(runner.getCopyContext().getTargetOrgId());
            perfGradeMapper.insertOrUpdateBatch(list);
        });

        runner.addCopyEntity(PerfPeriodPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return perfPeriodMapper.selectByOrgId(sourceOrgId);
        }, perfPeriodMapper::insertOrUpdateBatch);

        runner.addCopyEntity(PerfPO.class, runCtx -> {
            String sourceOrgId = runCtx.getCopyCtx().getSourceOrgId();
            return perfMapper.selectByOrgId(sourceOrgId);
        }, perfMapper::insertOrUpdateBatch);
    }

    public Pair<String, String> demoCopyFormula(DemoCopyRunner runner, String formula, String formulaExpCode) {
        ExpressionCalc expCalc = FormulaTypeEnum.parseExpression(formula);
        if (expCalc == null) {
            return Pair.of(formula, formulaExpCode);
        }
        expCalc.paramChipList().forEach(paramChip -> {
            if (paramChip.getParamBean() == null) {
                return;
            }
            if (paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_EVAL.getCode()
                || paramChip.getBizType() == FormulaTypeEnum.AOM_ACTV_OTHER.getCode()) {
                FormulaAomActvParam param = (FormulaAomActvParam) paramChip.getParamBean();
                Optional.ofNullable(runner.tryIdMapValue(AOM_REF_ID, param.getActvId(), param.getActvId()))
                    .ifPresent(pair -> param.setActvId(pair.getValue()));
                Optional.ofNullable(runner.tryIdMapValue(SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID, param.getSdIndicatorId(), param.getSdIndicatorId()))
                    .ifPresent(pair -> param.setSdIndicatorId(pair.getValue()));
                Optional.ofNullable(runner.tryIdMapValue(SPEVAL_DIM_SETTING_ID, param.getTypeId(), param.getTypeId()))
                    .ifPresent(pair -> param.setTypeId(pair.getValue()));
            } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_IMPORT.getCode()) {
                FormulaImportParam param = (FormulaImportParam) paramChip.getParamBean();
                Optional.ofNullable(runner.tryIdMapValue(SprvDemoOrgCopyConstants.SPRV_XPD_IMPT_ID, param.getImportId(), param.getImportId()))
                    .ifPresent(pair -> param.setImportId(pair.getValue()));
                Optional.ofNullable(runner.tryIdMapValue(SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID, param.getSdIndicatorId(), param.getSdIndicatorId()))
                    .ifPresent(pair -> param.setSdIndicatorId(pair.getValue()));
            } else if (paramChip.getBizType() == FormulaTypeEnum.PRI_PROFILE.getCode()) {
                String sdIndicatorId = (String) paramChip.getParamBean();
                Optional.ofNullable(runner.tryIdMapValue(SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID, sdIndicatorId, sdIndicatorId))
                    .ifPresent(pair -> paramChip.setParamBean(pair.getValue()));
            } else if (paramChip.getBizType() == FormulaTypeEnum.XPD_FAST.getCode()) {
                FormulaFastParam param = (FormulaFastParam) paramChip.getParamBean();
                Optional.ofNullable(runner.tryIdMapValue(SPEVAL_DIM_SETTING_ID, param.getTypeId(), param.getTypeId()))
                    .ifPresent(pair -> param.setTypeId(pair.getValue()));
                if (param.getRefIds() != null) {
                    for (FormulaFastParam.FastRefBO refId : param.getRefIds()) {
                        Optional.ofNullable(runner.tryIdMapValue(AOM_REF_ID, refId.getRefId(), refId.getRefId()))
                            .ifPresent(pair -> refId.setRefId(pair.getValue()));
                    }
                }
            }
        });
        Map<String, String> paramOldNewMap = new HashMap<>();
        String newFormula = expCalc.expressionShow(chip -> {
            String oldChipStr = chip.getChipStr();
            FormulaTypeEnum typeEnum = FormulaTypeEnum.getByCode(chip.getBizType());
            if (chip.getParamBean() != null && typeEnum != null) {
                String newChipStr = typeEnum.buildParam(chip.getParamBean());
                paramOldNewMap.put(oldChipStr, newChipStr);
                return newChipStr;
            }
            return chip.getChipStr();
        });
        String newFormulaExpCode = formulaExpCode;
        if (StringUtils.isNotEmpty(formulaExpCode)) {
            List<String> newList = JSON.parseArray(formulaExpCode, String.class);
            for (int i = 0; i < newList.size(); i++) {
                newList.set(i, paramOldNewMap.getOrDefault(newList.get(i), newList.get(i)));
            }
            newFormulaExpCode = JSON.toJSONString(newList);
        }
        return Pair.of(newFormula, newFormulaExpCode);
    }
}
