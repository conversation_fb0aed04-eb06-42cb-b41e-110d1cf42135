package com.yxt.talent.rv.application.xpd.dimcomb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.annotation.DbHintMaster;
import com.yxt.common.enums.YesOrNo;
import com.yxt.common.exception.ApiException;
import com.yxt.common.exception.ExceptionKey;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.*;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.command.XpdDimCombCreateCmd;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.command.XpdDimCombPutCmd;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.query.XpdDimCombQuery;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj.XpdDimCombListVO;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj.XpdDimCombVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.GridComb4Get;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombInfoVO;
import com.yxt.talent.rv.infrastructure.common.constant.AppConstants;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimCombPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import com.yxt.talent.rv.infrastructure.service.i18n.I18nTranslator;
import com.yxt.talent.rv.infrastructure.service.remote.GlobalAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.talent.rv.infrastructure.service.remote.UdpAclService;
import com.yxt.udpfacade.bean.org.OrgBean;
import jakarta.servlet.http.HttpServletRequest;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombInfoVO.Assembler.INSTANCE;
import static com.yxt.talent.rv.infrastructure.common.constant.AppConstants.TEMPLATE_XPD_ID;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils.createAmSlDrawer4RespDTO;

@Service
@AllArgsConstructor
@Slf4j
public class XpdDimCombAppService {

    private final XpdDimCombMapper xpdDimCombMapper;

    private final ILock lockService;

    private final GlobalAclService globalAclService;

    private final UdpAclService udpAclService;

    private final MessageSourceService messageSourceService;

    private final SptalentsdFacade sptalentsdFacade;

    private static final int MAX_LEASE_TIME = 100;

    public static final String LK_XPD_DIM_COMB = "sprv:lk:xpd:dim:comb:%s";

    private final I18nTranslator i18nTranslator;

    private final AuthService authService;

    private final XpdDimRuleMapper xpdDimRuleMapper;

    private final XpdGridDimCombMapper xpdGridDimCombMapper;
    private final XpdMapper xpdMapper;
    private final XpdDimMapper xpdDimMapper;

    /**
     * 保存维度组合
     */
    @DbHintMaster
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public String create(String orgId, String userId, XpdDimCombCreateCmd bean) {

        String lockKey = String.format(LK_XPD_DIM_COMB, orgId);
        if (lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
            try {
                if (bean.getXSdDimId().equals(bean.getYSdDimId())) {
                    throw new ApiException("apis.sptalentrv.xpd.dim.comb.dimId.error");
                }
                if (xpdDimCombMapper.countByName(orgId, bean.getCombName(), TEMPLATE_XPD_ID) > 0) {
                    throw new ApiException("apis.sptalentrv.xpd.dim.comb.name.conflict");
                }
                XpdDimCombPO entity = new XpdDimCombPO();
                BeanHelper.copyProperties(bean, entity);
                entity.setId(ApiUtil.getUuid());
                entity.setOrgId(orgId);
                entity.setDeleted(0);
                entity.setXpdId(TEMPLATE_XPD_ID);
                entity.setCombType(1);
                EntityUtil.setCreateInfo(userId, entity);
                entity.setCreateTime(LocalDateTime.now());
                entity.setUpdateTime(LocalDateTime.now());
                xpdDimCombMapper.insert(entity);
                return entity.getId();
            } catch (Exception e) {
                log.error(":createDimComb error", e);
                throw e;
            } finally {
                lockService.unLock(lockKey);
            }
        } else {
            throw new ApiException(ExceptionKey.DUPLICATE_REQUEST);
        }
    }

    /**
     * 更新维度组合
     *
     * @param orgId
     * @param userId
     */
    @Transactional(transactionManager = AppConstants.RV_TRANSACTION_MANAGER, rollbackFor = Exception.class)
    public void update(String orgId, String userId, XpdDimCombPutCmd bean) {

        if (bean.getXSdDimId().equals(bean.getYSdDimId())) {
            throw new ApiException("apis.sptalentrv.xpd.dim.comb.dimId.error");
        }

        XpdDimCombPO entity = xpdDimCombMapper.selectByPrimaryKey(bean.getId());
        if (entity == null) {
            // 维度组合不存在
            throw new ApiException("apis.sptalentrv.xpd.dim.comb.null");
        }
        if (xpdGridDimCombMapper.countByDimCombId(orgId, bean.getId()) > 0) {
            // 维度组合正在使用中
            throw new ApiException("apis.sptalentrv.xpd.dim.comb.inuse.notchange");
        }
        if (!entity.getCombName().equals(bean.getCombName())
                && xpdDimCombMapper.countByName(orgId, bean.getCombName(), TEMPLATE_XPD_ID) > 0) {
            throw new ApiException("apis.sptalentrv.xpd.dim.comb.name.conflict");
        }

        BeanHelper.copyProperties(bean, entity);
        entity.setOrgId(orgId);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(userId);
        EntityUtil.setUpdatedInfo(userId, entity);
        entity.setUpdateTime(LocalDateTime.now());
        xpdDimCombMapper.insertOrUpdate(entity);
    }

    /**
     * 删除维度组合
     *
     * @param orgId
     * @param userId
     * @param id
     */
    public void delete(String orgId, String userId, String id) {
        XpdDimCombPO entity = xpdDimCombMapper.selectByPrimaryKey(id);
        if (entity == null) {
            // 维度组合不存在
            throw new ApiException("apis.sptalentrv.xpd.dim.comb.null");
        }
        if (entity.getCombType() == 0) {
            // 内置组合不可删除
            throw new ApiException("apis.sptalentrv.xpd.dim.comb.notdeleted");
        }
        if (xpdGridDimCombMapper.countByDimCombId(orgId, id) > 0) {
            // 维度组合正在使用中，不可删除
            throw new ApiException("apis.sptalentrv.xpd.dim.comb.inuse.notchange");
        }

        entity.setDeleted(YesOrNo.YES.getValue());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(userId);
        xpdDimCombMapper.insertOrUpdate(entity);
    }

    /**
     * 维度组合详情
     *
     * @param orgId
     * @param id
     * @param lang
     * @return
     */
    public XpdDimCombVO getDetail(String orgId, String id, String lang) {
        XpdDimCombPO entity = new XpdDimCombPO();
        XpdDimCombVO result = new XpdDimCombVO();
        if (StringUtils.isNotBlank(id)) {
            entity = xpdDimCombMapper.selectByPrimaryKey(id);
            if (entity == null) {
                throw new ApiException("apis.sptalentrv.xpd.dim.comb.null");
            }
        }

        BeanHelper.copyProperties(entity, result);

        List<String> allDimIds = new ArrayList<>();
        allDimIds.add(entity.getXSdDimId());
        allDimIds.add(entity.getYSdDimId());

        List<DimensionList4Get> dimDetailList = sptalentsdFacade.getBaseDimDetail(orgId, allDimIds);
        Map<String, String> dimDetailMap = dimDetailList.stream()
                .collect(Collectors.toMap(DimensionList4Get::getId, DimensionList4Get::getDmName));

        result.setXSdDimName(dimDetailMap.get(entity.getXSdDimId()));
        result.setYSdDimName(dimDetailMap.get(entity.getYSdDimId()));

        //国际化
        OrgBean orgInfo = udpAclService.getOrgInfo(orgId);
        boolean enableI18n = orgInfo.getEnableI18n() == YesOrNo.YES.getValue();
        if (enableI18n) {
            String tranLang = messageSourceService.getLocalLanguageCode(lang);
            String key = entity.getCombNameI18n();
            String globalName = globalAclService.getTransByLangAndOrgId(key, tranLang, orgId);
            if (StringUtils.isNotBlank(globalName)) {
                result.setCombNameI18n(globalName);
            }
        }

        return result;
    }

    /**
     * 维度组合列表
     *
     * @param orgId
     * @param pageRequest
     * @return
     */
    public PagingList<XpdDimCombListVO> getPageList(String orgId, PageRequest pageRequest, XpdDimCombQuery query) {
        String xDimId = query.getXSdDimId();
        String yDimId = query.getYSdDimId();
        String combName = query.getCombName();
        String xpdId = query.getXpdId();
        Page<XpdDimCombListVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());

        // 当在项目中设置维度组合时，只能选择与项目规则中设置的维度相匹配的维度组
        Set<String> scopeSdDimIds = new HashSet<>();
        if (StringUtils.isNotBlank(query.getXpdId())) {
            XpdPO xpd = xpdMapper.selectByPrimaryKey(query.getXpdId());
            Validate.isNotNull(xpd, ExceptionKeys.XPD_NOT_EXIST);
            //            List<ModelDimDTO> modelDimInfos = spsdAclService.getModelDimInfo(orgId, xpd.getModelId());
            //            if (CollectionUtils.isEmpty(modelDimInfos)) {
            //                return new PagingList<>();
            //            }
            //            scopeSdDimIds = StreamUtil.map2set(modelDimInfos, ModelDimDTO::getDimId);
            //
            List<XpdDimPO> xpdDims = xpdDimMapper.selectByXpdId(orgId, xpdId, 0);
            scopeSdDimIds.addAll(StreamUtil.mapList(xpdDims, XpdDimPO::getSdDimId));
        }
        IPage<XpdDimCombListVO> combPage = xpdDimCombMapper.pagingByOrgId(page, orgId, xDimId, yDimId, combName,
                scopeSdDimIds);

        // 获取x轴y轴维度名称
        List<String> xSdDimIds = combPage.getRecords().stream().map(XpdDimCombListVO::getXSdDimId).distinct().toList();
        List<String> ySdDimIds = combPage.getRecords().stream().map(XpdDimCombListVO::getYSdDimId).distinct().toList();
        List<String> allDimIds = new ArrayList<>();
        allDimIds.addAll(xSdDimIds);
        allDimIds.addAll(ySdDimIds);

        List<DimensionList4Get> dimDetailList = sptalentsdFacade.getBaseDimDetail(orgId, allDimIds);

        Map<String, String> dimDetailMap = dimDetailList.stream()
                .collect(Collectors.toMap(DimensionList4Get::getId, DimensionList4Get::getDmName));

        combPage.getRecords().forEach(comb -> {
            comb.setXSdDimName(dimDetailMap.get(comb.getXSdDimId()));
            comb.setYSdDimName(dimDetailMap.get(comb.getYSdDimId()));
        });
        return BeanCopierUtil.toPagingList(combPage);

    }

    /**
     * 获取维度组列表
     *
     * @param xpdId
     * @return
     */
    public List<XpdDimCombInfoVO> getDimCombList(UserCacheDetail userCacheDetail, String xpdId) {
        String orgId = userCacheDetail.getOrgId();
        String locale = userCacheDetail.getLocale();
        List<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.listByXpdId(orgId, xpdId);
        return postProcessDimComb(orgId, xpdId, INSTANCE.toXpdDimCombVos(xpdDimCombs), locale);
    }

    private List<XpdDimCombInfoVO> postProcessDimComb(String orgId, String xpdId, List<XpdDimCombInfoVO> xpdDimCombVos,
            String locale) {
        if (CollectionUtils.isEmpty(xpdDimCombVos)) {
            return xpdDimCombVos;
        }
        List<String> sdDimIds = xpdDimCombVos.stream().flatMap(vo -> Stream.of(vo.getXSdDimId(), vo.getYSdDimId()))
                .collect(Collectors.toList());
        // 获取维度规则说明
        List<XpdDimRulePO> xpdDimRules = xpdDimRuleMapper.listBySdDimIds(orgId, xpdId, sdDimIds);
        Map<String, XpdDimRulePO> ruleMap = StreamUtil.list2map(xpdDimRules, XpdDimRulePO::getSdDimId);
        xpdDimCombVos.forEach(vo -> {
            XpdDimRulePO xSdDimRule = ruleMap.get(vo.getXSdDimId());
            XpdDimRulePO ySdDimRule = ruleMap.get(vo.getYSdDimId());
            vo.setXSdDimRuleDesc(xSdDimRule != null ? xSdDimRule.getRuleDesc() : "");
            vo.setYSdDimRuleDesc(ySdDimRule != null ? ySdDimRule.getRuleDesc() : "");
        });
        // 国际化
        translateDimCombList(orgId, locale, xpdDimCombVos, sdDimIds);
        return xpdDimCombVos;
    }

    private void translateDimCombList(String orgId, String locale, List<XpdDimCombInfoVO> xpdDimCombVos,
            List<String> sdDimIds) {
        List<DimensionList4Get> sdDimDetail = sptalentsdFacade.getBaseDimDetail(orgId, sdDimIds);
        Map<String, DimensionList4Get> dimDetailMap = sdDimDetail.stream()
                .collect(Collectors.toMap(DimensionList4Get::getId, Function.identity()));

        xpdDimCombVos.forEach(vo -> {
            DimensionList4Get xDim = dimDetailMap.get(vo.getXSdDimId());
            DimensionList4Get yDim = dimDetailMap.get(vo.getYSdDimId());
            if (xDim != null) {
                vo.setXSdDimName(xDim.getDmName());
                vo.setXSdDimNameI18n(xDim.getNameI18n());
            }
            if (yDim != null) {
                vo.setYSdDimName(yDim.getDmName());
                vo.setYSdDimNameI18n(yDim.getNameI18n());
            }
        });
        i18nTranslator.translate(orgId, locale, xpdDimCombVos);
    }

    public PagingList<GridComb4Get> getDimCombPage(HttpServletRequest request, String xpdId, String keyword, List<String> dimIds) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail(request);
        String orgId = userCacheDetail.getOrgId();
        String locale = userCacheDetail.getLocale();

        IPage<XpdDimCombPO> xpdDimCombs = xpdDimCombMapper.listByXpdIdPage(ApiUtil.toPage(request), orgId, xpdId, dimIds, keyword);
        PagingList<XpdDimCombInfoVO> pagingList = BeanCopierUtil.toPagingList(xpdDimCombs, XpdDimCombPO.class,
                XpdDimCombInfoVO.class);
        postProcessDimComb(orgId, xpdId, pagingList.getDatas(), locale);

        List<GridComb4Get> gridComb4Gets = pagingList.getDatas().stream().map(data -> {
            GridComb4Get gridComb4Get = new GridComb4Get();
            gridComb4Get.setId(data.getId());
            gridComb4Get.setName(data.getCombName());
            gridComb4Get.setCombname(
                    createAmSlDrawer4RespDTO(data.getCombName(), data.getId(), data.getId(), data.getCombName()));
            gridComb4Get.setXsddimid(data.getXSdDimName());
            gridComb4Get.setYsddimid(data.getYSdDimName());
            return gridComb4Get;
        }).collect(Collectors.toList());

        return new PagingList<>(gridComb4Gets, pagingList.getPaging());
    }

    /**
     * 根据宫格id获取盘点宫格下的维度组信息
     *
     * @param orgId
     * @param xpdId
     * @param gridId
     * @return
     */
    public List<XpdDimCombInfoVO> getXpdGridDimCombs(String orgId, String xpdId, String gridId) {
        UserCacheDetail userCache = authService.getUserCacheDetail();
        List<XpdDimCombPO> dimCombList = xpdDimCombMapper.listByXpdIdAndGridId(orgId, xpdId, gridId);
        return postProcessDimComb(orgId, xpdId, INSTANCE.toXpdDimCombVos(dimCombList), userCache.getLocale());
    }
}
