<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper">
    <resultMap id="BaseResultMap" type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO">
        <!--@mbg.generated-->
        <!--@Table rv_calimeet-->
        <result column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="xpd_id" jdbcType="CHAR" property="xpdId"/>
        <result column="calimeet_status" jdbcType="TINYINT" property="calimeetStatus"/>
        <result column="calimeet_name" jdbcType="VARCHAR" property="calimeetName"/>
        <result column="calimeet_mode" jdbcType="TINYINT" property="calimeetMode"/>
        <result column="calimeet_type" jdbcType="TINYINT" property="calimeetType"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="show_ratio" jdbcType="TINYINT" property="showRatio"/>
        <result column="record" jdbcType="LONGVARCHAR" property="record"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, org_id, xpd_id, calimeet_status, calimeet_name, calimeet_mode, calimeet_type,
        start_time, end_time, show_ratio, record, deleted, create_user_id, create_time, update_user_id,
        update_time
    </sql>
    <insert id="insert" parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO">
        <!--@mbg.generated-->
        insert into rv_calimeet (id, org_id, xpd_id, calimeet_status,
        calimeet_name, calimeet_mode, calimeet_type,
        start_time, end_time, show_ratio,
        record, deleted, create_user_id,
        create_time, update_user_id, update_time
        )
        values (#{id,jdbcType=CHAR}, #{orgId,jdbcType=CHAR}, #{xpdId,jdbcType=CHAR}, #{calimeetStatus,jdbcType=TINYINT},
        #{calimeetName,jdbcType=VARCHAR}, #{calimeetMode,jdbcType=TINYINT}, #{calimeetType,jdbcType=TINYINT},
        #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{showRatio,jdbcType=TINYINT},
        #{record,jdbcType=LONGVARCHAR}, #{deleted,jdbcType=TINYINT}, #{createUserId,jdbcType=CHAR},
        #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_calimeet
        (id, org_id, xpd_id, calimeet_status, calimeet_name, calimeet_mode, calimeet_type,
        start_time, end_time, show_ratio, record, deleted, create_user_id, create_time,
        update_user_id, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR}, #{item.orgId,jdbcType=CHAR}, #{item.xpdId,jdbcType=CHAR},
            #{item.calimeetStatus,jdbcType=TINYINT}, #{item.calimeetName,jdbcType=VARCHAR},
            #{item.calimeetMode,jdbcType=TINYINT}, #{item.calimeetType,jdbcType=TINYINT},
            #{item.startTime,jdbcType=TIMESTAMP},
            #{item.endTime,jdbcType=TIMESTAMP}, #{item.showRatio,jdbcType=TINYINT}, #{item.record,jdbcType=LONGVARCHAR},
            #{item.deleted,jdbcType=TINYINT}, #{item.createUserId,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateUserId,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="updateById">
        update rv_calimeet
        <set>
            <if test="calimeetStatus != null">
                calimeet_status = #{calimeetStatus},
            </if>
            <if test="calimeetName != null">
                calimeet_name = #{calimeetName},
            </if>
            <if test="calimeetMode != null">
                calimeet_mode = #{calimeetMode},
            </if>
            <if test="calimeetType != null">
                calimeet_type = #{calimeetType},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
<!--            <if test="endTime != null">-->
                end_time = #{endTime},
<!--            </if>-->
            <if test="showRatio != null">
                show_ratio = #{showRatio},
            </if>
            <if test="record != null">
                record = #{record},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteByIdAndOrgId">
        update rv_calimeet set deleted = 1 where org_id=#{orgId} and id =#{id} and deleted=0
    </delete>

    <insert id="insertOrUpdate">
        insert into rv_calimeet
        (id,
        org_id,
        xpd_id,
        calimeet_status,
        calimeet_name,
        calimeet_mode,
        calimeet_type,
        start_time,
        end_time,
        show_ratio,
        record,
        deleted,
        create_user_id,
        create_time,
        update_user_id,
        update_time)
        values
        (#{element.id},
        #{element.orgId},
        #{element.xpdId},
        #{element.calimeetStatus},
        #{element.calimeetName},
        #{element.calimeetMode},
        #{element.calimeetType},
        #{element.startTime},
        #{element.endTime},
        #{element.showRatio},
        #{element.record},
        #{element.deleted},
        #{element.createUserId},
        #{element.createTime},
        #{element.updateUserId},
        #{element.updateTime})
        on duplicate key update
        <trim suffixOverrides=",">
            xpd_id = values(xpd_id),
            calimeet_status = values(calimeet_status),
            calimeet_name = values(calimeet_name),
            calimeet_mode = values(calimeet_mode),
            calimeet_type = values(calimeet_type),
            start_time = values(start_time),
            end_time = values(end_time),
            show_ratio = values(show_ratio),
            record = values(record),
            deleted = values(deleted)
        </trim>
    </insert>

    <select id="countByOrgIdAndId" resultType="java.lang.Integer">
        select count(1) from rv_calimeet where org_id = #{orgId} and id = #{id}
    </select>

    <select id="selectByIdAndOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet
        where org_id = #{orgId}
        and id = #{id}
        and deleted = 0
    </select>
    <select id="pageQuery"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet c
        where c.org_id = #{orgId}
        and c.xpd_id = #{xpdId}
        and c.deleted = 0
        <if test="name != null and name != ''">
            and c.calimeet_name like concat('%',#{name},'%')
        </if>
        <if test="statusList != null and statusList.size > 0">
            and c.calimeet_status in
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="authIds != null and authIds.size > 0">
            and (c.create_user_id in
            <foreach collection="authIds" item="uid" open="(" separator="," close=")">
                #{uid}
            </foreach>
            or EXISTS ( select 1 from rv_calimeet_participants where org_id =#{orgId}
            and user_id in
            <foreach collection="authIds" item="uid" open="(" separator="," close=")">
                #{uid}
            </foreach>
            and user_type =1
            and calimeet_id = c.id))
        </if>
        order by c.create_time desc
    </select>

    <update id="deleteMeetingByXpdId">
        update rv_calimeet
        set deleted = 1,
        update_time = now(),
        update_user_id = #{operatorId}
        where org_id = #{orgId}
        and xpd_id = #{xpdId}
    </update>

    <select id="clientPage" resultType="com.yxt.talent.rv.controller.client.general.meet.viewobj.MeetClientVO">
      SELECT
      b.id,
      b.calimeet_name AS meetName,
      b.xpd_id AS projectId,
      b.calimeet_status AS meetStatus,
      b.start_time AS startTime,
      b.end_time AS endTime
      FROM rv_calimeet b
      INNER JOIN rv_xpd c ON b.org_id = c.org_id AND b.xpd_id = c.id
      WHERE
      b.org_id = #{orgId}
      AND c.deleted = 0
      AND b.deleted = 0
      and b.calimeet_mode = 0
      and b.calimeet_status in (1,2)
      AND EXISTS (
      SELECT 1
      FROM rv_calimeet_participants a
      WHERE
      a.org_id = b.org_id
      AND a.calimeet_id = b.id
      AND a.user_id = #{userId}
      AND a.deleted = 0
      and a.user_type = 2


      <if test="search.caliStatus != null">
        and a.cali_status = #{search.caliStatus}
      </if>
      )



        <if test="search.meetStatus != null and search.meetStatus != -1">
            and b.calimeet_status = #{search.meetStatus}
        </if>

        <if test=" search.keyword != null and search.keyword != ''">
            and (b.calimeet_name like concat('%', #{search.escapedKeyword}, '%') escape '\\')
        </if>
        order by b.create_time desc
    </select>
    <select id="selectByOrgIdAndIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet
        where
        org_id = #{orgId,jdbcType=VARCHAR}
        <choose>
            <when test="ids != null and ids.size() > 0">
                AND id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                and 1 != 1
            </otherwise>
        </choose>
    </select>
    <select id="selectListByXpdIdAndOrgId"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_calimeet
        where
        org_id = #{orgId}
        and xpd_id =#{xpdId}
        and deleted = 0
    </select>

  <select id="select4RefreshCaliMeetUserResult" resultType="java.lang.String">
    select distinct a.id from rv_calimeet a join rv_xpd b on a.xpd_id = b.id AND b.deleted = 0 where a.deleted = 0
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from rv_calimeet
    where id = #{id}
  </select>

  <update id="transferResource">
    update rv_calimeet
    set create_user_id = #{toUserId},
        update_time = now()
    where create_user_id = #{fromUserId}
      and org_id = #{orgId}
      and deleted = 0
  </update>

  <select id="countByUserScope" resultType="int">
    select count(distinct a.id)
    from rv_calimeet a
    where a.org_id = #{orgId}
      and a.deleted = 0
      and (
      a.create_user_id = #{userId}
        or
      exists(select 1
             from rv_calimeet_participants b
             where b.user_id = #{userId}
               and b.org_id = #{orgId}
               and b.calimeet_id = a.id
               and b.user_type = 1 -- 只转移组织者
      )
      )
  </select>
</mapper>