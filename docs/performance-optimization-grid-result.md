# 宫格查询性能优化文档

## 问题描述

原有的`getDimCombGridResult`接口存在性能问题：
- 每个宫格会返回所有的人员数据
- 当宫格中人员数量较多时，查询和数据传输性能较差
- Java层面需要处理大量数据进行排序和分组

## 优化方案

### 1. 添加宫格用户数量限制参数

在`XpdResultQuery`类中添加了`gridCellUserLimit`参数：
- 默认值：50
- 用途：限制每个宫格返回的最大用户数量
- 提供getter方法确保参数有效性

### 2. SQL层面优化

修改了`getDimCombGridResult`的SQL查询：

#### 原有查询结构：
```sql
-- 简单的用户维度查询，返回所有用户
select ... from user_dim_levels u
inner join rv_xpd_grid_cell d on ...
ORDER BY u.x_index, u.y_index
```

#### 优化后的查询结构：
```sql
-- 使用CTE和窗口函数限制每个宫格的用户数量
with user_dim_levels as (...),
user_with_cell as (...),
ranked_users as (
    select *,
           ROW_NUMBER() OVER (
               PARTITION BY cellId 
               ORDER BY xIndex ASC, yIndex ASC, fullname ASC, userId ASC
           ) as row_num
    from user_with_cell
)
select ... from ranked_users
where row_num <= #{query.gridCellUserLimit}
ORDER BY xIndex, yIndex, row_num
```

### 3. 性能监控

添加了详细的性能监控日志：
- `LOG21001`: 记录查询开始时间和参数
- `LOG21002`: 记录查询完成时间、耗时和返回用户数

## 优化效果

### 预期性能提升：
1. **数据库查询性能**：通过窗口函数在SQL层面限制数据量，减少数据传输
2. **内存使用**：减少Java应用内存占用
3. **网络传输**：减少前后端数据传输量
4. **用户体验**：提高页面加载速度

### 兼容性：
- 保持原有接口签名不变
- 保持原有排序逻辑（Java层面精确排序）
- 向后兼容，不影响现有功能

## 使用方式

### 前端调用示例：
```javascript
// 默认限制50个用户
const query = {
    queryType: 2,
    targetId: 'dimCombId',
    // gridCellUserLimit 可选，默认50
};

// 自定义限制数量
const queryWithLimit = {
    queryType: 2,
    targetId: 'dimCombId',
    gridCellUserLimit: 100  // 每个宫格最多返回100个用户
};
```

### 后端配置：
无需额外配置，优化自动生效。

## 监控和调优

### 性能监控：
通过日志监控查询性能：
```
LOG21001:开始查询宫格数据 orgId=xxx, xpdId=xxx, dimCombId=xxx, gridCellUserLimit=50
LOG21002:宫格数据查询完成 orgId=xxx, xpdId=xxx, 查询耗时=123ms, 返回用户数=450
```

### 调优建议：
1. 根据实际业务需求调整`gridCellUserLimit`默认值
2. 监控查询耗时，必要时进一步优化SQL
3. 考虑添加数据库索引优化排序性能

## 注意事项

1. **数据完整性**：每个宫格只返回前N个用户，如需查看全部用户需要额外的分页查询接口
2. **排序一致性**：SQL层面的排序与Java层面的排序保持一致
3. **参数验证**：确保`gridCellUserLimit`参数的有效性（大于0）

## 后续优化方向

1. **更智能的排序**：在SQL中实现更复杂的业务排序逻辑
2. **缓存优化**：对频繁查询的宫格数据进行缓存
3. **分页查询**：为宫格详情提供分页查询接口
4. **索引优化**：根据查询模式优化数据库索引
